/**
 * 温湿度折线图视图
 *
 * 使用MPAndroidChart库实现的实时数据图表：
 * 1. 图表配置：
 *    - 单Y轴显示数据
 *    - X轴显示时间（mm:ss格式）
 *    - 支持缩放和拖动
 *    - 自适应暗色/亮色主题
 *
 * 2. 数据显示：
 *    - 单一数据线条
 *    - 实时数据更新
 *    - 最近100秒数据滚动显示
 *
 * 3. 交互特性：
 *    - 支持触摸操作
 *    - 支持缩放查看
 *    - 自动调整坐标轴范围
 *
 * 4. 性能优化：
 *    - 高效的数据更新机制
 *    - 平滑的动画效果
 *    - 内存优化的数据结构
 */

package no.nordicsemi.android.uart.view

import android.content.Context
import android.graphics.Color
import android.graphics.DashPathEffect
import android.util.Log
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.formatter.*
import com.github.mikephil.charting.interfaces.datasets.ILineDataSet
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.concurrent.ConcurrentLinkedQueue

private const val MAX_TIME_WINDOW = 100  // 100秒
private const val POINTS_PER_SECOND = 50 // 50Hz采样率
private const val VISIBLE_DATA_POINTS = MAX_TIME_WINDOW * POINTS_PER_SECOND // 5000点
private const val Y_AXIS_MIN = 0f      // Y轴最小值 (16位数据范围)
private const val Y_AXIS_MAX = 65535f  // Y轴最大值 (16位数据范围)

@Composable
internal fun LineChartView(
    dataPoints: List<Int>,
    label: String,
    color: Int,
    zoomIn: Boolean,
    modifier: Modifier = Modifier
) {
    val isSystemInDarkTheme = isSystemInDarkTheme()

    // 添加数据接收日志，包含图表标识
//    Log.d("LineChartView", "[${label}] 接收到新数据: size=${dataPoints.size}, " +
//            "前5个数据点=${dataPoints.take(5)}, " +
//            "后5个数据点=${dataPoints.takeLast(5)}")

    // 缓存图表实例和标签
    val chart = remember {
        mutableStateOf<LineChart?>(null)
    }
    val chartLabel = remember {
        mutableStateOf(label)
    }

    // 使用remember缓存上一次的数据点和更新时间
    val lastDataPoints = remember {
        mutableStateOf<List<Int>>(emptyList())
    }
    val lastUpdateTime = remember {
        mutableStateOf(0L)
    }

    // 创建协程作用域
    val scope = rememberCoroutineScope()

    // 创建图表视图
    AndroidView(
        modifier = modifier
            .fillMaxWidth()
            .height(300.dp),
        factory = { context ->
//            Log.d("LineChartView", "[${chartLabel.value}] 创建图表实例")
            createLineChartView(isSystemInDarkTheme, context, dataPoints, chartLabel.value, color, zoomIn).also {
                chart.value = it
            }
        },
        update = { view ->
            // 检查数据是否实际变化且是否满足最小更新间隔
            val currentTime = System.currentTimeMillis()
            if (!dataPoints.contentEquals(lastDataPoints.value) &&
                currentTime - lastUpdateTime.value >= 200) { // 200ms最小更新间隔

//                Log.d("LineChartView", "[${chartLabel.value}] 检测到数据实际变化，当前数据点数量: ${dataPoints.size}, " +
//                        "上次数据点数量: ${lastDataPoints.value.size}")

                lastDataPoints.value = dataPoints.toList()
                lastUpdateTime.value = currentTime

                // 使用单一协程更新图表
                scope.launch(Dispatchers.Default) {
                    updateChartData(view, dataPoints, chartLabel.value, color, zoomIn)
                }
            }
        }
    )

    // 使用LaunchedEffect处理清理工作
    DisposableEffect(Unit) {
        onDispose {
//            Log.d("LineChartView", "[${chartLabel.value}] 清理图表资源")
            chart.value?.clear()
        }
    }
}

// 添加List<Int>的内容比较扩展函数
private fun List<Int>.contentEquals(other: List<Int>): Boolean {
    if (size != other.size) return false
    return indices.all { this[it] == other[it] }
}

private suspend fun updateChartData(
    chart: LineChart,
    dataPoints: List<Int>,
    label: String,
    color: Int,
    zoomIn: Boolean
) = withContext(Dispatchers.Default) {
    try {
//        Log.d("LineChartView", "[$label] 开始更新图表数据，数据点数量: ${dataPoints.size}")
//        Log.d("LineChartView", "[$label] EntryPool stats before update: ${EntryPool.getStats()}")

        // 在后台线程准备数据
        val entries = ArrayList<Entry>(dataPoints.size)
        dataPoints.forEachIndexed { i, v ->
            val timeInSeconds = (i.toFloat() / POINTS_PER_SECOND)
            entries.add(EntryPool.acquire(timeInSeconds, v.toFloat()))
        }

        val dataSet = createLineDataSet(entries, label, chart.isDarkTheme(), color)
        val dataSets = ArrayList<ILineDataSet>().apply {
            add(dataSet)
        }

        // 在主线程更新图表
        withContext(Dispatchers.Main) {
            Log.d("LineChartView", "[$label] 在主线程更新图表")
            chart.apply {
                // 释放旧的Entry对象
                data?.let { oldData ->
                    oldData.dataSets.forEach { dataset ->
                        if (dataset is LineDataSet) {
                            val oldEntries = dataset.values
                            if (oldEntries != null) {
                                EntryPool.releaseAll(oldEntries)
                            }
                        }
                    }
                }

                data = LineData(dataSets)

                axisLeft.apply {
                    axisMaximum = Y_AXIS_MAX
                    axisMinimum = Y_AXIS_MIN
                }

                xAxis.apply {
                    val timeRange = dataPoints.size.toFloat() / POINTS_PER_SECOND
                    val minVisibleRange = 10f  // 最小显示10秒

                    if (timeRange <= minVisibleRange) {
                        axisMaximum = minVisibleRange
                        axisMinimum = 0f
                    } else {
                        val visibleTimeRange = minOf(timeRange, MAX_TIME_WINDOW.toFloat())
                        axisMaximum = timeRange
                        axisMinimum = maxOf(0f, timeRange - visibleTimeRange)
                    }
                }

                setVisibleXRangeMaximum(MAX_TIME_WINDOW.toFloat())
                setVisibleXRangeMinimum(10f)
                moveViewToX(dataPoints.size.toFloat() / POINTS_PER_SECOND)
                animateX(300)
            }
        }

//        Log.d("LineChartView", "[$label] EntryPool stats after update: ${EntryPool.getStats()}")
    } catch (e: Exception) {
//        Log.e("LineChartView", "[$label] 更新图表数据失败", e)
    }
}

private fun createLineChartView(
    isDarkTheme: Boolean,
    context: Context,
    dataPoints: List<Int>,
    label: String,
    color: Int,
    zoomIn: Boolean
): LineChart {
    return LineChart(context).apply {
        description.isEnabled = false
        legend.isEnabled = false

        setTouchEnabled(true)
        setDragEnabled(true)
        setScaleEnabled(true)
        setPinchZoom(true)

        // 设置主题颜色
        setupThemeColors(isDarkTheme)

        // 配置X轴
        setupXAxis()

        // 配置Y轴
        setupYAxis(dataPoints, zoomIn)

        // 禁用右侧Y轴
        axisRight.isEnabled = false

        // 初始化数据
        val entries = dataPoints.mapIndexed { i, v ->
            // 将索引转换为时间（秒）
            val timeInSeconds = (i.toFloat() / POINTS_PER_SECOND)
            Entry(timeInSeconds, v.toFloat())
        }

        val dataSet = createLineDataSet(entries, label, isDarkTheme, color)
        val dataSets = ArrayList<ILineDataSet>().apply {
            add(dataSet)
        }

        data = LineData(dataSets)
    }
}

private fun LineChart.setupThemeColors(isDarkTheme: Boolean) {
    if (isDarkTheme) {
        setBackgroundColor(Color.TRANSPARENT)
        xAxis.gridColor = Color.WHITE
        xAxis.textColor = Color.WHITE
        axisLeft.gridColor = Color.WHITE
        axisLeft.textColor = Color.WHITE
    } else {
        setBackgroundColor(Color.WHITE)
        xAxis.gridColor = Color.BLACK
        xAxis.textColor = Color.BLACK
        axisLeft.gridColor = Color.BLACK
        axisLeft.textColor = Color.BLACK
    }
}

private fun LineChart.setupXAxis() {
    xAxis.apply {
        enableGridDashedLine(10f, 10f, 0f)
        // 初始显示10秒范围
        axisMinimum = 0f
        axisMaximum = 10f  // 初始显示10秒
        setAvoidFirstLastClipping(true)
        position = XAxis.XAxisPosition.BOTTOM

        valueFormatter = object : ValueFormatter() {
            override fun getFormattedValue(value: Float): String {
                // value 现在直接表示秒数
                val minutes = value.toInt() / 60
                val seconds = value.toInt() % 60
                return String.format("%02d:%02d", minutes, seconds)
            }
        }
    }
}

private fun LineChart.setupYAxis(dataPoints: List<Int>, zoomIn: Boolean) {
    axisLeft.apply {
        enableGridDashedLine(10f, 10f, 0f)
        axisMaximum = Y_AXIS_MAX
        axisMinimum = Y_AXIS_MIN
    }
}

private fun createLineDataSet(
    entries: List<Entry>,
    label: String,
    isDarkTheme: Boolean,
    lineColor: Int
): LineDataSet {
    return LineDataSet(entries, label).apply {
        setDrawIcons(false)
        setDrawValues(false)
        setDrawCircles(false)
        mode = LineDataSet.Mode.CUBIC_BEZIER

        color = lineColor
        lineWidth = 1.5f

        formLineWidth = 1f
        formLineDashEffect = DashPathEffect(floatArrayOf(10f, 5f), 0f)
        formSize = 15f
        valueTextSize = 9f

        enableDashedHighlightLine(10f, 5f, 0f)
    }
}

private fun LineChart.isDarkTheme(): Boolean {
    return xAxis.textColor == Color.WHITE
}

private fun List<Int>.getMax(zoomIn: Boolean): Float {
    if (isEmpty()) return 100f
    val max = maxOrNull()!!
    return if (zoomIn) (max * 1.2f) else (max * 1.5f)
}

private fun List<Int>.getMin(zoomIn: Boolean): Float {
    if (isEmpty()) return 0f
    val min = minOrNull()!!
    return if (zoomIn) (min * 0.8f) else (min * 0.5f)
}

